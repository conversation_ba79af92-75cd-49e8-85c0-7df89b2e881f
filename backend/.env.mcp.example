# DigitalOcean MCP Server Configuration
# Copy this file to .env.mcp.local and fill in your actual values

# DigitalOcean API Token (required)
# Get this from: https://cloud.digitalocean.com/account/api/tokens
DIGITALOCEAN_TOKEN=your_digitalocean_api_token_here

# MCP Server Configuration
MCP_SERVER_HOST=0.0.0.0
MCP_SERVER_PORT=8001

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=digitalocean_mcp.log

# Optional: Default droplet configuration
DEFAULT_DROPLET_REGION=nyc1
DEFAULT_DROPLET_SIZE=s-1vcpu-1gb
DEFAULT_DROPLET_IMAGE=ubuntu-22-04-x64
