# DigitalOcean MCP Server

This directory contains the DigitalOcean Model Context Protocol (MCP) server implementation for the Goali project. The MCP server provides AI assistants with the ability to manage DigitalOcean infrastructure through a standardized protocol.

## Overview

The DigitalOcean MCP server enables AI assistants to:
- List and manage DigitalOcean droplets
- Get detailed droplet information
- Create new droplets
- Power cycle droplets
- Access account information
- Monitor infrastructure status

## Features

### MCP Tools Available

1. **`list_droplets`** - List all droplets in your DigitalOcean account
2. **`get_droplet_info`** - Get detailed information about a specific droplet
3. **`create_droplet`** - Create a new droplet with specified configuration
4. **`power_cycle_droplet`** - Power cycle a droplet
5. **`get_account_info`** - Get DigitalOcean account information

### HTTP Endpoints

- `GET /` - Server status
- `GET /health` - Health check with DigitalOcean connectivity test
- `GET /droplets` - List droplets via HTTP

## Setup

### 1. Environment Configuration

Copy the example environment file and configure your settings:

```bash
cp .env.mcp.example .env.mcp.local
```

Edit `.env.mcp.local` and set your DigitalOcean API token:

```bash
DIGITALOCEAN_TOKEN=your_digitalocean_api_token_here
MCP_SERVER_HOST=0.0.0.0
MCP_SERVER_PORT=8001
```

### 2. Get DigitalOcean API Token

1. Go to [DigitalOcean API Tokens](https://cloud.digitalocean.com/account/api/tokens)
2. Click "Generate New Token"
3. Give it a name (e.g., "Goali MCP Server")
4. Select "Read" and "Write" scopes
5. Copy the generated token

### 3. Install Dependencies

Dependencies are automatically installed when you build the Docker containers:

```bash
docker compose -f backend/docker-compose.yml build
```

### 4. Verification

Run the verification script to ensure everything is configured correctly:

```bash
python backend/scripts/verify_digitalocean_mcp.py
```

## Usage

### Starting the Server

#### Option 1: Docker Compose (Recommended)

```bash
# Set your DigitalOcean token
export DIGITALOCEAN_TOKEN=your_token_here

# Start all services including MCP server
docker compose -f backend/docker-compose.yml up -d

# Check MCP server logs
docker logs backend-digitalocean-mcp-1
```

#### Option 2: Standalone

```bash
# Set environment variables
export DIGITALOCEAN_TOKEN=your_token_here
export MCP_SERVER_PORT=8001

# Start the server
python backend/scripts/start_digitalocean_mcp.py
```

### Testing the Server

```bash
# Health check
curl http://localhost:8001/health

# List droplets
curl http://localhost:8001/droplets

# Check server status
curl http://localhost:8001/
```

### Using with AI Assistants

Configure your AI assistant to connect to the MCP server:

- **Server URL**: `http://localhost:8001`
- **Protocol**: Model Context Protocol (MCP)
- **Available Tools**: See the tools list above

## Configuration

### Environment Variables

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `DIGITALOCEAN_TOKEN` | Yes | - | DigitalOcean API token |
| `MCP_SERVER_HOST` | No | `0.0.0.0` | Server bind address |
| `MCP_SERVER_PORT` | No | `8001` | Server port |
| `LOG_LEVEL` | No | `INFO` | Logging level |

### Default Droplet Settings

You can set default values for droplet creation:

```bash
DEFAULT_DROPLET_REGION=nyc1
DEFAULT_DROPLET_SIZE=s-1vcpu-1gb
DEFAULT_DROPLET_IMAGE=ubuntu-22-04-x64
```

## Security

### API Token Security

- **Never commit** your DigitalOcean API token to version control
- Use environment variables or secure secret management
- Rotate tokens regularly
- Use tokens with minimal required permissions

### Network Security

- The MCP server runs on localhost by default
- For production, consider using HTTPS and authentication
- Restrict network access to trusted clients only

## Troubleshooting

### Common Issues

1. **"DIGITALOCEAN_TOKEN not set"**
   - Ensure you've set the environment variable
   - Check that the token is valid and not expired

2. **"Failed to connect to DigitalOcean API"**
   - Verify your API token has correct permissions
   - Check your internet connection
   - Ensure DigitalOcean API is accessible

3. **"Port already in use"**
   - Change the `MCP_SERVER_PORT` environment variable
   - Check if another service is using port 8001

### Debugging

Enable debug logging:

```bash
export LOG_LEVEL=DEBUG
python backend/scripts/start_digitalocean_mcp.py
```

Check server logs:

```bash
docker logs -f backend-digitalocean-mcp-1
```

## Development

### Adding New Tools

1. Add the tool function to `DigitalOceanMCPServer._register_tools()`
2. Use the `@self.mcp_server.tool("tool_name")` decorator
3. Return a dictionary with `success` and relevant data
4. Handle exceptions gracefully

### Testing

Run the verification script during development:

```bash
python backend/scripts/verify_digitalocean_mcp.py
```

## Support

For issues related to:
- **DigitalOcean API**: Check [DigitalOcean API Documentation](https://docs.digitalocean.com/reference/api/)
- **MCP Protocol**: Check [Model Context Protocol Specification](https://modelcontextprotocol.io/)
- **This Implementation**: Create an issue in the project repository
