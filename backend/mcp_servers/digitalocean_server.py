"""
DigitalOcean MCP Server Implementation

This module provides a Model Context Protocol (MCP) server that integrates with DigitalOcean
to provide droplet management, monitoring, and infrastructure operations.
"""

import os
import json
import asyncio
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

import digitalocean
from mcp import MCPServer, Tool, Resource
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from pydantic import BaseModel


@dataclass
class DigitalOceanConfig:
    """Configuration for DigitalOcean MCP server"""
    token: str
    server_host: str = "0.0.0.0"
    server_port: int = 8001
    

class DropletInfo(BaseModel):
    """Pydantic model for droplet information"""
    id: int
    name: str
    status: str
    ip_address: Optional[str]
    region: str
    size: str
    image: str
    created_at: str


class DigitalOceanMCPServer:
    """DigitalOcean MCP Server for managing DO resources"""
    
    def __init__(self, config: DigitalOceanConfig):
        self.config = config
        self.manager = digitalocean.Manager(token=config.token)
        self.mcp_server = MCPServer()
        self.app = FastAPI(title="DigitalOcean MCP Server")
        
        # Register MCP tools
        self._register_tools()
        self._register_resources()
        self._setup_routes()
    
    def _register_tools(self):
        """Register MCP tools for DigitalOcean operations"""
        
        @self.mcp_server.tool("list_droplets")
        async def list_droplets() -> Dict[str, Any]:
            """List all droplets in the DigitalOcean account"""
            try:
                droplets = self.manager.get_all_droplets()
                droplet_list = []
                
                for droplet in droplets:
                    droplet_info = {
                        "id": droplet.id,
                        "name": droplet.name,
                        "status": droplet.status,
                        "ip_address": droplet.ip_address,
                        "region": droplet.region["name"] if droplet.region else None,
                        "size": droplet.size_slug,
                        "image": droplet.image["name"] if droplet.image else None,
                        "created_at": droplet.created_at
                    }
                    droplet_list.append(droplet_info)
                
                return {
                    "success": True,
                    "droplets": droplet_list,
                    "count": len(droplet_list)
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e)
                }
        
        @self.mcp_server.tool("get_droplet_info")
        async def get_droplet_info(droplet_id: int) -> Dict[str, Any]:
            """Get detailed information about a specific droplet"""
            try:
                droplet = self.manager.get_droplet(droplet_id)
                
                return {
                    "success": True,
                    "droplet": {
                        "id": droplet.id,
                        "name": droplet.name,
                        "status": droplet.status,
                        "ip_address": droplet.ip_address,
                        "private_ip_address": droplet.private_ip_address,
                        "region": droplet.region,
                        "size": droplet.size,
                        "image": droplet.image,
                        "created_at": droplet.created_at,
                        "features": droplet.features,
                        "backup_ids": droplet.backup_ids,
                        "snapshot_ids": droplet.snapshot_ids,
                        "volume_ids": droplet.volume_ids,
                        "tags": droplet.tags
                    }
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e)
                }
        
        @self.mcp_server.tool("create_droplet")
        async def create_droplet(
            name: str,
            region: str = "nyc1",
            image: str = "ubuntu-22-04-x64",
            size_slug: str = "s-1vcpu-1gb",
            ssh_keys: Optional[List[str]] = None
        ) -> Dict[str, Any]:
            """Create a new droplet"""
            try:
                droplet = digitalocean.Droplet(
                    token=self.config.token,
                    name=name,
                    region=region,
                    image=image,
                    size_slug=size_slug,
                    ssh_keys=ssh_keys or []
                )
                
                droplet.create()
                
                return {
                    "success": True,
                    "droplet_id": droplet.id,
                    "message": f"Droplet '{name}' creation initiated"
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e)
                }
        
        @self.mcp_server.tool("power_cycle_droplet")
        async def power_cycle_droplet(droplet_id: int) -> Dict[str, Any]:
            """Power cycle a droplet"""
            try:
                droplet = self.manager.get_droplet(droplet_id)
                action = droplet.power_cycle()
                
                return {
                    "success": True,
                    "action_id": action.id,
                    "message": f"Power cycle initiated for droplet {droplet_id}"
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e)
                }
        
        @self.mcp_server.tool("get_account_info")
        async def get_account_info() -> Dict[str, Any]:
            """Get DigitalOcean account information"""
            try:
                account = self.manager.get_account()
                
                return {
                    "success": True,
                    "account": {
                        "droplet_limit": account.droplet_limit,
                        "floating_ip_limit": account.floating_ip_limit,
                        "email": account.email,
                        "uuid": account.uuid,
                        "email_verified": account.email_verified,
                        "status": account.status
                    }
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e)
                }
    
    def _register_resources(self):
        """Register MCP resources"""
        
        @self.mcp_server.resource("digitalocean://droplets")
        async def droplets_resource() -> Dict[str, Any]:
            """Resource providing current droplet status"""
            droplets = self.manager.get_all_droplets()
            return {
                "content": [
                    {
                        "type": "text",
                        "text": json.dumps([
                            {
                                "id": d.id,
                                "name": d.name,
                                "status": d.status,
                                "ip": d.ip_address
                            } for d in droplets
                        ], indent=2)
                    }
                ]
            }
    
    def _setup_routes(self):
        """Setup FastAPI routes"""
        
        @self.app.get("/")
        async def root():
            return {"message": "DigitalOcean MCP Server is running"}
        
        @self.app.get("/health")
        async def health_check():
            try:
                # Test DO API connectivity
                account = self.manager.get_account()
                return {
                    "status": "healthy",
                    "digitalocean_connected": True,
                    "account_email": account.email
                }
            except Exception as e:
                return {
                    "status": "unhealthy",
                    "digitalocean_connected": False,
                    "error": str(e)
                }
        
        @self.app.get("/droplets")
        async def list_droplets_http():
            """HTTP endpoint to list droplets"""
            try:
                droplets = self.manager.get_all_droplets()
                return {
                    "droplets": [
                        DropletInfo(
                            id=d.id,
                            name=d.name,
                            status=d.status,
                            ip_address=d.ip_address,
                            region=d.region["name"] if d.region else "unknown",
                            size=d.size_slug,
                            image=d.image["name"] if d.image else "unknown",
                            created_at=d.created_at
                        ).dict() for d in droplets
                    ]
                }
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
    
    async def start(self):
        """Start the MCP server"""
        import uvicorn
        
        config = uvicorn.Config(
            app=self.app,
            host=self.config.server_host,
            port=self.config.server_port,
            log_level="info"
        )
        
        server = uvicorn.Server(config)
        await server.serve()


def create_digitalocean_mcp_server() -> DigitalOceanMCPServer:
    """Factory function to create DigitalOcean MCP server"""
    
    # Get configuration from environment
    token = os.getenv("DIGITALOCEAN_TOKEN")
    if not token:
        raise ValueError("DIGITALOCEAN_TOKEN environment variable is required")
    
    config = DigitalOceanConfig(
        token=token,
        server_host=os.getenv("MCP_SERVER_HOST", "0.0.0.0"),
        server_port=int(os.getenv("MCP_SERVER_PORT", "8001"))
    )
    
    return DigitalOceanMCPServer(config)


if __name__ == "__main__":
    # Run the server
    server = create_digitalocean_mcp_server()
    asyncio.run(server.start())
