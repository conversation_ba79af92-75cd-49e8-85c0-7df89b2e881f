services:
  web:
    build: .
    tty: true
    stdin_open: true
    env_file:
      - .env         # Load base environment variables
      - .env.dev   # Load debug specific variables if needed
      # REMOVED .env.test - Test env vars should not be loaded by the dev service
    ports:
      - "8000:8000"      # Django server
      - "5678:5678"      # Django server debugging (debugpy in web service)
      # Removed 5680 from here, will be exposed by celery service
    volumes:
      - .:/usr/src/app  # Mount local code for live reloading
    depends_on:
      db:
        condition: service_healthy
      celery:
        condition: service_started
      redis:
        condition: service_started
    environment:
      - SERVICE_NAME=web
      - DJANGO_SETTINGS_MODULE=config.settings.dev # Explicitly set dev settings
      - DATABASE_URL=************************************/mydb
      - CELERY_BROKER_URL=redis://redis:6379/0
      - PYTHONPATH=/usr/src/app
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
      - DJANGO_DEBUG=1
      - WAIT_FOR_DEBUGGER=${WAIT_FOR_DEBUGGER:-false}  # Default to false if not set
    # Extend shutdown timeout to allow for debugging sessions
    stop_grace_period: 1m
    command: >
      bash -c "/usr/src/app/entrypoint.sh setup &&
              if [ \"${WAIT_FOR_DEBUGGER}\" = \"true\" ]; then
                  exec python debug_django.py --wait-for-debugger
              else
                  exec uvicorn config.asgi:application --host 0.0.0.0 --port 8000 --workers 1 --reload
              fi"

  web-test:
    build: .
    env_file:
      - .env.test
    volumes:
      - .:/usr/src/app
    depends_on:
      redis:
        condition: service_started
      test-db:
        condition: service_healthy  # Using service_healthy since test-db has healthcheck defined
    environment:
      - DJANGO_SETTINGS_MODULE=config.settings.test
      - TESTING=true
      - PYTHONPATH=/usr/src/app
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
      - DJANGO_SKIP_CHECKS=1
      - DJANGO_ALLOW_ASYNC_UNSAFE=true
      - SKIP_SEEDER_IDEMPOTENCY_CHECK=true
      - ERROR_REPORT_FILE=/usr/src/app/test-results/error_report.txt
    # Updated command to use the consolidated setup script
    command: >
      bash -c "echo 'Setting up test environment with Python script...' &&
          python ultimate_test_setup.py &&
          echo 'Starting test runner...' &&
          python -Xfrozen_modules=off -m pytest -c pytest.ini --reuse-db -p testing.error_reporter_plugin"

  debug-tests:
    extends: web-test
    ports:
      - "5681:5681"
    environment:
      - PYTEST_ARGS=${PYTEST_ARGS:-}
      - SKIP_SEEDER_IDEMPOTENCY_CHECK=true
    command: >
      bash -c "echo 'Setting up test environment... Args: $PYTEST_ARGS' &&
              bash scripts/setup_test_env.sh &&
              echo 'Starting test runner with debugger... Args: $PYTEST_ARGS' &&
              python -Xfrozen_modules=off -m debugpy --wait-for-client --listen 0.0.0.0:5681 $PYTEST_ARGS"

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: mydb
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      retries: 5
      timeout: 5s
    # Persist database data between container restarts
    restart: unless-stopped

  # Redis service used as Celery broker and Channels layer backend
  redis:
    image: redis:latest
    ports:
      - "6379:6379" # Exposes Redis default port to the host
    # Persist Redis data between container restarts
    restart: unless-stopped

  test-db:
    image: postgres:15
    environment:
      POSTGRES_DB: test_goali
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user"]
      interval: 10s
      retries: 5
      timeout: 5s
    restart: unless-stopped

  celery:
    build: .
    # Run celery via debugpy, listening on port 5680
    ports: # Expose celery debug port
      - "5680:5680"
    volumes:
      - .:/usr/src/app
    depends_on:
      - redis
      - db
    environment:
      - SERVICE_NAME=celery
      - DATABASE_URL=************************************/mydb
      - CELERY_BROKER_URL=redis://redis:6379/0
      - PYTHONPATH=/usr/src/app
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    command: >
      bash -c "/usr/src/app/entrypoint.sh setup &&
              python -m debugpy --listen 0.0.0.0:5680 -m celery -A config worker --loglevel=info"

  # Grafana service for benchmark visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_SECURITY_ADMIN_USER=admin
      - GF_INSTALL_PLUGINS=grafana-clock-panel
      - GF_PATHS_PROVISIONING=/etc/grafana/provisioning
      - GF_ANALYTICS_REPORTING_ENABLED=false
      - GF_ANALYTICS_CHECK_FOR_UPDATES=false
      - GF_SERVER_ROOT_URL=http://localhost:3000
      - GF_AUTH_ANONYMOUS_ENABLED=false
      - GF_AUTH_DISABLE_LOGIN_FORM=false
      - GF_SECURITY_DISABLE_INITIAL_ADMIN_CREATION=false
      - GF_LOG_LEVEL=info
      - GF_LIVE_ALLOWED_ORIGINS=http://localhost:3000
      - GF_FEATURE_TOGGLES_ENABLE=
    volumes:
      - grafana_data:/var/lib/grafana
      - ../monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ../monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped

  # Prometheus for metrics collection (optional, for system metrics)
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ../monitoring/prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  # DigitalOcean MCP Server
  digitalocean-mcp:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: backend-digitalocean-mcp-1
    ports:
      - "8001:8001"
    environment:
      - DIGITALOCEAN_TOKEN=${DIGITALOCEAN_TOKEN}
      - MCP_SERVER_HOST=0.0.0.0
      - MCP_SERVER_PORT=8001
      - LOG_LEVEL=INFO
    volumes:
      - .:/usr/src/app
    command: python scripts/start_digitalocean_mcp.py
    depends_on:
      - redis
    restart: unless-stopped

volumes:
  postgres_data:
  postgres_test_data:
  grafana_data:
  prometheus_data:
