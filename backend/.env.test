# backend/.env.test
# Environment variables for testing

# Django settings module
DJANGO_SETTINGS_MODULE=config.settings.test

# Database Configuration for Tests
# Smart environment detection will override this based on Docker vs local environment
DATABASE_URL=***********************************************/test_goali

# Django settings
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1

# Set testing flag for test discovery
TESTING=true

# Execution Mode Configuration
# CRITICAL: Set to 'testing' to allow mock mode and fallbacks during tests
GOALI_DEFAULT_EXECUTION_MODE=testing

# Flags moved from pytest.ini
PYTEST_DJANGO_AUTODISCOVER=0
DJANGO_SKIP_CHECKS=1
DJANGO_ALLOW_ASYNC_UNSAFE=true

DEBUG_MODE=true
DJANGO_DEBUG=true

# Redis Configuration for Tests
CELERY_BROKER_URL=redis://redis:6379/0

# LLM Configuration
DEFAULT_LLM_MODEL_NAME="mistral-small-latest"
DEFAULT_LLM_TEMPERATURE=0.7
DEFAULT_LLM_INPUT_TOKEN_PRICE="0.1"
DEFAULT_LLM_OUTPUT_TOKEN_PRICE="0.3"

# LLM API Keys for Tests
# Real API key for Docker environment (when available)
MISTRAL_API_KEY=j43C8qMj0UsVHRlVVmURHATwEvK7b7Zu
# Dummy API keys for local testing (will be detected as test keys)
OPENAI_API_KEY=test_openai_key_for_testing

# Other test-specific settings
SECRET_KEY=test_secret_key_for_testing_only

# DigitalOcean MCP Configuration (for testing)
DIGITALOCEAN_TOKEN=test_digitalocean_token_for_testing