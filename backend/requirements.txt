#
# This file is autogenerated by pip-compile with Python 3.12
# by the following command:
#
#    pip-compile --output-file=requirements.txt requirements.in
#
aenum==3.1.16
    # via deepgram-sdk
aiofiles==24.1.0
    # via deepgram-sdk
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.11.18
    # via deepgram-sdk
aiosignal==1.3.2
    # via aiohttp
amqp==5.3.1
    # via kombu
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   httpx
    #   mcp
    #   sse-starlette
    #   starlette
asgiref==3.8.1
    # via
    #   channels
    #   channels-redis
    #   daphne
    #   django
    #   django-allauth
    #   django-cors-headers
attrs==25.3.0
    # via
    #   aiohttp
    #   jsonschema
    #   referencing
    #   service-identity
    #   twisted
autobahn==24.4.2
    # via daphne
automat==25.4.16
    # via twisted
babel==2.17.0
    # via py-moneyed
backoff==2.2.1
    # via -r requirements.in
billiard==4.2.1
    # via celery
build==1.2.2.post1
    # via pip-tools
celery==5.5.2
    # via -r requirements.in
certifi==2025.4.26
    # via
    #   httpcore
    #   httpx
    #   requests
cffi==1.17.1
    # via cryptography
channels==4.2.2
    # via
    #   -r requirements.in
    #   channels-redis
channels-redis==4.2.1
    # via -r requirements.in
chardet==5.2.0
    # via -r requirements.in
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via
    #   celery
    #   click-didyoumean
    #   click-plugins
    #   click-repl
    #   coverage-lcov
    #   pip-tools
    #   uvicorn
click-didyoumean==0.3.1
    # via celery
click-plugins==1.1.1
    # via celery
click-repl==0.3.0
    # via celery
constantly==23.10.4
    # via twisted
contourpy==1.3.2
    # via matplotlib
coverage[toml]==7.8.1
    # via
    #   coverage-badge
    #   coverage-lcov
    #   pytest-cov
coverage-badge==1.1.2
    # via -r requirements.in
coverage-lcov==0.3.0
    # via -r requirements.in
cryptography==45.0.2
    # via
    #   -r requirements.in
    #   autobahn
    #   pyopenssl
    #   service-identity
cycler==0.12.1
    # via matplotlib
daphne==4.2.0
    # via -r requirements.in
dataclasses-json==0.6.7
    # via deepgram-sdk
deepgram-sdk==4.1.0
    # via -r requirements.in
deprecation==2.1.0
    # via deepgram-sdk
diff-match-patch==20241021
    # via django-import-export
dj-database-url==3.0.0
    # via -r requirements.in
django==5.2.1
    # via
    #   -r requirements.in
    #   channels
    #   dj-database-url
    #   django-allauth
    #   django-cors-headers
    #   django-debug-toolbar
    #   django-extensions
    #   django-import-export
    #   django-money
    #   django-polymorphic
    #   django-redis
    #   djangorestframework
django-allauth==65.8.1
    # via -r requirements.in
django-cors-headers==4.7.0
    # via -r requirements.in
django-debug-toolbar==5.2.0
    # via -r requirements.in
django-environ==0.12.0
    # via -r requirements.in
django-extensions==4.1
    # via -r requirements.in
django-import-export==4.3.7
    # via -r requirements.in
django-money==3.5.4
    # via -r requirements.in
django-polymorphic==4.1.0
    # via -r requirements.in
django-prometheus==2.3.1
    # via -r requirements.in
django-redis==5.4.0
    # via -r requirements.in
djangorestframework==3.16.0
    # via -r requirements.in
eval-type-backport==0.2.2
    # via mistralai
execnet==2.1.1
    # via pytest-xdist
factory-boy==3.3.3
    # via -r requirements.in
faker==37.3.0
    # via factory-boy
fastapi==0.115.12
    # via -r requirements.in
flake8==7.2.0
    # via -r requirements.in
fonttools==4.58.0
    # via matplotlib
frozenlist==1.6.0
    # via
    #   aiohttp
    #   aiosignal
greenlet==3.2.3
    # via sqlalchemy
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via
    #   deepgram-sdk
    #   langgraph-sdk
    #   langsmith
    #   mcp
    #   mistralai
httpx-sse==0.4.0
    # via mcp
hyperlink==21.0.0
    # via
    #   autobahn
    #   twisted
idna==3.10
    # via
    #   anyio
    #   httpx
    #   hyperlink
    #   requests
    #   twisted
    #   yarl
incremental==24.7.2
    # via twisted
iniconfig==2.1.0
    # via pytest
jinja2==3.1.6
    # via pytest-html
jsonpatch==1.33
    # via langchain-core
jsonpickle==4.1.1
    # via python-digitalocean
jsonpointer==3.0.0
    # via jsonpatch
jsonschema==4.23.0
    # via -r requirements.in
jsonschema-specifications==2025.4.1
    # via jsonschema
kiwisolver==1.4.8
    # via matplotlib
kombu==5.5.3
    # via celery
langchain==0.3.25
    # via -r requirements.in
langchain-core==0.3.60
    # via
    #   langchain
    #   langchain-text-splitters
    #   langgraph
    #   langgraph-checkpoint
    #   langgraph-prebuilt
langchain-text-splitters==0.3.8
    # via langchain
langgraph==0.4.5
    # via -r requirements.in
langgraph-checkpoint==2.0.26
    # via
    #   langgraph
    #   langgraph-prebuilt
langgraph-prebuilt==0.1.8
    # via langgraph
langgraph-sdk==0.1.70
    # via langgraph
langsmith==0.3.42
    # via
    #   langchain
    #   langchain-core
markupsafe==3.0.2
    # via jinja2
marshmallow==3.26.1
    # via dataclasses-json
matplotlib==3.10.3
    # via -r requirements.in
mccabe==0.7.0
    # via flake8
mcp==1.9.4
    # via -r requirements.in
mistralai==1.7.0
    # via -r requirements.in
msgpack==1.1.0
    # via channels-redis
multidict==6.4.4
    # via
    #   aiohttp
    #   yarl
mypy-extensions==1.1.0
    # via typing-inspect
narwhals==1.40.0
    # via plotly
numpy==2.2.6
    # via
    #   -r requirements.in
    #   contourpy
    #   matplotlib
    #   pandas
    #   scipy
orjson==3.10.18
    # via
    #   langgraph-sdk
    #   langsmith
ormsgpack==1.9.1
    # via langgraph-checkpoint
packaging==24.2
    # via
    #   build
    #   deprecation
    #   langchain-core
    #   langsmith
    #   marshmallow
    #   matplotlib
    #   plotly
    #   pytest
pandas==2.2.3
    # via -r requirements.in
pillow==11.2.1
    # via matplotlib
pip-tools==7.4.1
    # via -r requirements.in
plotly==6.1.1
    # via -r requirements.in
pluggy==1.6.0
    # via pytest
prometheus-client==0.22.0
    # via
    #   -r requirements.in
    #   django-prometheus
prompt-toolkit==3.0.51
    # via click-repl
propcache==0.3.1
    # via
    #   aiohttp
    #   yarl
psycopg2-binary==2.9.10
    # via -r requirements.in
py-moneyed==3.0
    # via django-money
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   service-identity
pyasn1-modules==0.4.2
    # via service-identity
pycodestyle==2.13.0
    # via flake8
pycparser==2.22
    # via cffi
pydantic==2.11.4
    # via
    #   -r requirements.in
    #   fastapi
    #   langchain
    #   langchain-core
    #   langgraph
    #   langsmith
    #   mcp
    #   mistralai
    #   pydantic-settings
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.9.1
    # via mcp
pydot==4.0.1
    # via -r requirements.in
pyflakes==3.3.2
    # via flake8
pyopenssl==25.1.0
    # via twisted
pyparsing==3.2.3
    # via
    #   -r requirements.in
    #   matplotlib
    #   pydot
pyproject-hooks==1.2.0
    # via
    #   build
    #   pip-tools
pytest==8.3.5
    # via
    #   pytest-asyncio
    #   pytest-cov
    #   pytest-django
    #   pytest-html
    #   pytest-metadata
    #   pytest-mock
    #   pytest-xdist
pytest-asyncio==0.26.0
    # via -r requirements.in
pytest-cov==6.1.1
    # via -r requirements.in
pytest-django==4.11.1
    # via -r requirements.in
pytest-html==4.1.1
    # via -r requirements.in
pytest-metadata==3.1.1
    # via pytest-html
pytest-mock==3.14.0
    # via -r requirements.in
pytest-xdist==3.7.0
    # via -r requirements.in
python-dateutil==2.9.0.post0
    # via
    #   celery
    #   matplotlib
    #   mistralai
    #   pandas
python-digitalocean==1.17.0
    # via -r requirements.in
python-dotenv==1.1.0
    # via pydantic-settings
python-multipart==0.0.20
    # via mcp
python-slugify==8.0.4
    # via -r requirements.in
pytz==2025.2
    # via pandas
pyyaml==6.0.2
    # via
    #   langchain
    #   langchain-core
redis==6.1.0
    # via
    #   -r requirements.in
    #   channels-redis
    #   django-redis
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.3
    # via
    #   langchain
    #   langsmith
    #   python-digitalocean
    #   requests-toolbelt
requests-toolbelt==1.0.0
    # via langsmith
rpds-py==0.25.1
    # via
    #   jsonschema
    #   referencing
scipy==1.15.3
    # via -r requirements.in
semver==3.0.4
    # via -r requirements.in
service-identity==24.2.0
    # via twisted
six==1.17.0
    # via python-dateutil
sniffio==1.3.1
    # via anyio
sqlalchemy==2.0.41
    # via langchain
sqlparse==0.5.3
    # via
    #   django
    #   django-debug-toolbar
sse-starlette==2.3.6
    # via mcp
starlette==0.46.2
    # via
    #   fastapi
    #   mcp
tablib==3.8.0
    # via django-import-export
tenacity==9.1.2
    # via langchain-core
text-unidecode==1.3
    # via python-slugify
twisted[tls]==24.11.0
    # via daphne
txaio==23.1.1
    # via autobahn
typing-extensions==4.13.2
    # via
    #   anyio
    #   deepgram-sdk
    #   dj-database-url
    #   fastapi
    #   langchain-core
    #   py-moneyed
    #   pydantic
    #   pydantic-core
    #   pyopenssl
    #   referencing
    #   sqlalchemy
    #   twisted
    #   typing-inspect
    #   typing-inspection
typing-inspect==0.9.0
    # via dataclasses-json
typing-inspection==0.4.1
    # via
    #   mistralai
    #   pydantic
    #   pydantic-settings
tzdata==2025.2
    # via
    #   faker
    #   kombu
    #   pandas
urllib3==2.4.0
    # via requests
uvicorn==0.34.2
    # via
    #   -r requirements.in
    #   mcp
vine==5.1.0
    # via
    #   amqp
    #   celery
    #   kombu
wcwidth==0.2.13
    # via prompt-toolkit
websockets==15.0.1
    # via deepgram-sdk
wheel==0.45.1
    # via pip-tools
xxhash==3.5.0
    # via langgraph
yarl==1.20.0
    # via aiohttp
zope-interface==7.2
    # via twisted
zstandard==0.23.0
    # via langsmith

# The following packages are considered to be unsafe in a requirements file:
# pip
# setuptools
