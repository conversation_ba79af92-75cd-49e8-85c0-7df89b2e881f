#!/usr/bin/env python3
"""
Startup script for DigitalOcean MCP Server

This script initializes and starts the DigitalOcean MCP server with proper
configuration and error handling.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from mcp_servers.digitalocean_server import create_digitalocean_mcp_server


def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('digitalocean_mcp.log')
        ]
    )


def validate_environment():
    """Validate required environment variables"""
    required_vars = ['DIGITALOCEAN_TOKEN']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("\nPlease set the following environment variables:")
        for var in missing_vars:
            print(f"  export {var}=your_value_here")
        return False
    
    return True


async def main():
    """Main function to start the DigitalOcean MCP server"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 Starting DigitalOcean MCP Server...")
    
    # Validate environment
    if not validate_environment():
        sys.exit(1)
    
    try:
        # Create and start the server
        server = create_digitalocean_mcp_server()
        logger.info(f"✅ Server configured for {server.config.server_host}:{server.config.server_port}")
        
        # Test DigitalOcean connectivity
        logger.info("🔍 Testing DigitalOcean API connectivity...")
        account = server.manager.get_account()
        logger.info(f"✅ Connected to DigitalOcean account: {account.email}")
        
        # Start the server
        logger.info("🌐 Starting MCP server...")
        await server.start()
        
    except Exception as e:
        logger.error(f"❌ Failed to start DigitalOcean MCP server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
