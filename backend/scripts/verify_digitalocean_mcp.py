#!/usr/bin/env python3
"""
DigitalOcean MCP Server Verification Script

This script verifies that the DigitalOcean MCP server is properly configured
and can connect to the DigitalOcean API.
"""

import os
import sys
import asyncio
import json
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))


def check_environment():
    """Check if required environment variables are set"""
    print("🔍 Checking environment configuration...")
    
    required_vars = {
        'DIGITALOCEAN_TOKEN': 'DigitalOcean API token'
    }
    
    optional_vars = {
        'MCP_SERVER_HOST': 'MCP server host (default: 0.0.0.0)',
        'MCP_SERVER_PORT': 'MCP server port (default: 8001)'
    }
    
    missing_required = []
    
    print("\n📋 Required Environment Variables:")
    for var, description in required_vars.items():
        value = os.getenv(var)
        if value:
            # Mask sensitive values
            display_value = f"{value[:8]}..." if len(value) > 8 else "***"
            print(f"  ✅ {var}: {display_value} ({description})")
        else:
            print(f"  ❌ {var}: Not set ({description})")
            missing_required.append(var)
    
    print("\n📋 Optional Environment Variables:")
    for var, description in optional_vars.items():
        value = os.getenv(var)
        if value:
            print(f"  ✅ {var}: {value} ({description})")
        else:
            print(f"  ⚪ {var}: Using default ({description})")
    
    return len(missing_required) == 0, missing_required


def check_dependencies():
    """Check if required Python packages are installed"""
    print("\n🔍 Checking Python dependencies...")
    
    required_packages = [
        'digitalocean',
        'mcp',
        'fastapi',
        'uvicorn',
        'pydantic'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}: Installed")
        except ImportError:
            print(f"  ❌ {package}: Not installed")
            missing_packages.append(package)
    
    return len(missing_packages) == 0, missing_packages


async def test_digitalocean_connection():
    """Test connection to DigitalOcean API"""
    print("\n🔍 Testing DigitalOcean API connection...")
    
    try:
        import digitalocean
        
        token = os.getenv('DIGITALOCEAN_TOKEN')
        if not token:
            print("  ❌ DIGITALOCEAN_TOKEN not set")
            return False
        
        manager = digitalocean.Manager(token=token)
        
        # Test API connection by getting account info
        print("  🔄 Fetching account information...")
        account = manager.get_account()
        
        print(f"  ✅ Connected to DigitalOcean account: {account.email}")
        print(f"  📊 Droplet limit: {account.droplet_limit}")
        print(f"  📊 Account status: {account.status}")
        
        # Test listing droplets
        print("  🔄 Fetching droplets...")
        droplets = manager.get_all_droplets()
        print(f"  📊 Found {len(droplets)} droplets")
        
        if droplets:
            print("  📋 Droplet summary:")
            for droplet in droplets[:3]:  # Show first 3 droplets
                status_emoji = "🟢" if droplet.status == "active" else "🔴"
                print(f"    {status_emoji} {droplet.name} ({droplet.status}) - {droplet.ip_address}")
            
            if len(droplets) > 3:
                print(f"    ... and {len(droplets) - 3} more")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Failed to connect to DigitalOcean API: {e}")
        return False


async def test_mcp_server_creation():
    """Test MCP server creation"""
    print("\n🔍 Testing MCP server creation...")
    
    try:
        from mcp_servers.digitalocean_server import create_digitalocean_mcp_server
        
        print("  🔄 Creating DigitalOcean MCP server...")
        server = create_digitalocean_mcp_server()
        
        print(f"  ✅ MCP server created successfully")
        print(f"  📊 Server host: {server.config.server_host}")
        print(f"  📊 Server port: {server.config.server_port}")
        print(f"  📊 DigitalOcean token configured: {'Yes' if server.config.token else 'No'}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Failed to create MCP server: {e}")
        return False


def print_next_steps(all_checks_passed):
    """Print next steps based on verification results"""
    print("\n" + "="*60)
    
    if all_checks_passed:
        print("🎉 All checks passed! DigitalOcean MCP server is ready to use.")
        print("\n📋 Next steps:")
        print("1. Start the MCP server:")
        print("   python backend/scripts/start_digitalocean_mcp.py")
        print("\n2. Test the server endpoints:")
        print("   curl http://localhost:8001/health")
        print("   curl http://localhost:8001/droplets")
        print("\n3. Configure your AI assistant to use the MCP server:")
        print("   - MCP Server URL: http://localhost:8001")
        print("   - Available tools: list_droplets, get_droplet_info, create_droplet, etc.")
        
    else:
        print("❌ Some checks failed. Please fix the issues above before proceeding.")
        print("\n📋 Common solutions:")
        print("1. Set your DigitalOcean API token:")
        print("   export DIGITALOCEAN_TOKEN=your_token_here")
        print("\n2. Install missing dependencies:")
        print("   pip install -r backend/requirements.txt")
        print("\n3. Get a DigitalOcean API token:")
        print("   https://cloud.digitalocean.com/account/api/tokens")


async def main():
    """Main verification function"""
    print("🚀 DigitalOcean MCP Server Verification")
    print("="*60)
    
    # Run all checks
    env_ok, missing_env = check_environment()
    deps_ok, missing_deps = check_dependencies()
    
    if not env_ok or not deps_ok:
        print_next_steps(False)
        return
    
    do_ok = await test_digitalocean_connection()
    mcp_ok = await test_mcp_server_creation()
    
    all_checks_passed = env_ok and deps_ok and do_ok and mcp_ok
    print_next_steps(all_checks_passed)


if __name__ == "__main__":
    asyncio.run(main())
