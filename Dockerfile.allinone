# Dockerfile.allinone - FINAL FIXED VERSION
# All-in-one container for cost-effective Digital Ocean deployment
# Includes: Frontend (React), Backend (Django), Database (PostgreSQL), Cache (Redis), Web Server (Nginx)

FROM ubuntu:22.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Set timezone
ENV TZ=UTC
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-dev \
    python3-venv \
    python3-pip \
    postgresql \
    postgresql-contrib \
    redis-server \
    nginx \
    supervisor \
    curl \
    wget \
    git \
    build-essential \
    pkg-config \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js 20.x (LTS)
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
RUN apt-get install -y nodejs && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Create application user
RUN useradd -m -s /bin/bash goali

# Create redis user if it doesn't exist
RUN id -u redis &>/dev/null || useradd -r -s /bin/false redis

# Set working directory
WORKDIR /app

# Copy all source code
COPY . .

# Build frontend first
WORKDIR /app/frontend
RUN npm install && npm run build

# Install Python dependencies
WORKDIR /app/backend
RUN python3 -m pip install --upgrade pip
RUN python3 -m pip install pip-tools
RUN pip-compile requirements.in || echo "pip-compile failed, using existing requirements.txt"
RUN python3 -m pip install -r requirements.txt
RUN python3 -m pip install gunicorn uvicorn psycopg2-binary

# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/data /app/static /app/media
RUN mkdir -p /var/run/postgresql
RUN mkdir -p /var/log/supervisor

# CRITICAL FIX: Remove conflicting PostgreSQL directory and use custom location
# The Ubuntu package creates /var/lib/postgresql/14/main with files, causing initdb conflicts
# Solution: Use a clean directory for our database
RUN rm -rf /var/lib/postgresql/14/main
RUN mkdir -p /app/postgresql/data
RUN chown -R postgres:postgres /app/postgresql/ /var/run/postgresql/
RUN chmod 700 /app/postgresql/data
RUN chmod 775 /var/run/postgresql

# Initialize PostgreSQL in our custom location as postgres user
USER postgres
RUN /usr/lib/postgresql/14/bin/initdb -D /app/postgresql/data --auth-local=trust --auth-host=md5
RUN echo "local all all trust" > /app/postgresql/data/pg_hba.conf
RUN echo "host all all 127.0.0.1/32 trust" >> /app/postgresql/data/pg_hba.conf
RUN echo "host all all ::1/128 trust" >> /app/postgresql/data/pg_hba.conf

# Start PostgreSQL temporarily to create database and user
RUN /usr/lib/postgresql/14/bin/pg_ctl -D /app/postgresql/data -l /tmp/postgres.log start && \
    sleep 5 && \
    createdb goali_prod && \
    psql -c "CREATE USER goali WITH PASSWORD 'secure_generated_password';" && \
    psql -c "GRANT ALL PRIVILEGES ON DATABASE goali_prod TO goali;" && \
    psql -c "ALTER USER goali CREATEDB;" && \
    /usr/lib/postgresql/14/bin/pg_ctl -D /app/postgresql/data stop

# Switch back to root for final configuration
USER root

# CRITICAL FIX: Configure Redis to work with supervisor (no daemon mode)
RUN sed -i 's/^daemonize yes/daemonize no/' /etc/redis/redis.conf
RUN sed -i 's/^# maxmemory <bytes>/maxmemory 256mb/' /etc/redis/redis.conf
RUN sed -i 's/^# maxmemory-policy noeviction/maxmemory-policy allkeys-lru/' /etc/redis/redis.conf
RUN chown redis:redis /etc/redis/redis.conf

# Create Nginx configuration
RUN echo 'server {' > /etc/nginx/sites-available/goali && \
    echo '    listen 8080;' >> /etc/nginx/sites-available/goali && \
    echo '    server_name _;' >> /etc/nginx/sites-available/goali && \
    echo '    client_max_body_size 10M;' >> /etc/nginx/sites-available/goali && \
    echo '' >> /etc/nginx/sites-available/goali && \
    echo '    # Frontend' >> /etc/nginx/sites-available/goali && \
    echo '    location / {' >> /etc/nginx/sites-available/goali && \
    echo '        root /app/frontend/dist;' >> /etc/nginx/sites-available/goali && \
    echo '        index index.html;' >> /etc/nginx/sites-available/goali && \
    echo '        try_files $uri $uri/ /index.html;' >> /etc/nginx/sites-available/goali && \
    echo '    }' >> /etc/nginx/sites-available/goali && \
    echo '' >> /etc/nginx/sites-available/goali && \
    echo '    # API endpoints' >> /etc/nginx/sites-available/goali && \
    echo '    location /api/ {' >> /etc/nginx/sites-available/goali && \
    echo '        proxy_pass http://127.0.0.1:8001;' >> /etc/nginx/sites-available/goali && \
    echo '        proxy_set_header Host $host;' >> /etc/nginx/sites-available/goali && \
    echo '        proxy_set_header X-Real-IP $remote_addr;' >> /etc/nginx/sites-available/goali && \
    echo '        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;' >> /etc/nginx/sites-available/goali && \
    echo '        proxy_set_header X-Forwarded-Proto $scheme;' >> /etc/nginx/sites-available/goali && \
    echo '    }' >> /etc/nginx/sites-available/goali && \
    echo '' >> /etc/nginx/sites-available/goali && \
    echo '    # WebSocket endpoints' >> /etc/nginx/sites-available/goali && \
    echo '    location /ws/ {' >> /etc/nginx/sites-available/goali && \
    echo '        proxy_pass http://127.0.0.1:8001;' >> /etc/nginx/sites-available/goali && \
    echo '        proxy_http_version 1.1;' >> /etc/nginx/sites-available/goali && \
    echo '        proxy_set_header Upgrade $http_upgrade;' >> /etc/nginx/sites-available/goali && \
    echo '        proxy_set_header Connection "upgrade";' >> /etc/nginx/sites-available/goali && \
    echo '        proxy_set_header Host $host;' >> /etc/nginx/sites-available/goali && \
    echo '        proxy_read_timeout 86400s;' >> /etc/nginx/sites-available/goali && \
    echo '    }' >> /etc/nginx/sites-available/goali && \
    echo '' >> /etc/nginx/sites-available/goali && \
    echo '    # Admin' >> /etc/nginx/sites-available/goali && \
    echo '    location /admin/ {' >> /etc/nginx/sites-available/goali && \
    echo '        proxy_pass http://127.0.0.1:8001;' >> /etc/nginx/sites-available/goali && \
    echo '        proxy_set_header Host $host;' >> /etc/nginx/sites-available/goali && \
    echo '        proxy_set_header X-Real-IP $remote_addr;' >> /etc/nginx/sites-available/goali && \
    echo '    }' >> /etc/nginx/sites-available/goali && \
    echo '' >> /etc/nginx/sites-available/goali && \
    echo '    # Health check' >> /etc/nginx/sites-available/goali && \
    echo '    location /health/ {' >> /etc/nginx/sites-available/goali && \
    echo '        proxy_pass http://127.0.0.1:8001;' >> /etc/nginx/sites-available/goali && \
    echo '        access_log off;' >> /etc/nginx/sites-available/goali && \
    echo '    }' >> /etc/nginx/sites-available/goali && \
    echo '' >> /etc/nginx/sites-available/goali && \
    echo '    # Static files' >> /etc/nginx/sites-available/goali && \
    echo '    location /static/ {' >> /etc/nginx/sites-available/goali && \
    echo '        alias /app/static/;' >> /etc/nginx/sites-available/goali && \
    echo '        expires 1y;' >> /etc/nginx/sites-available/goali && \
    echo '    }' >> /etc/nginx/sites-available/goali && \
    echo '' >> /etc/nginx/sites-available/goali && \
    echo '    location /media/ {' >> /etc/nginx/sites-available/goali && \
    echo '        alias /app/media/;' >> /etc/nginx/sites-available/goali && \
    echo '        expires 30d;' >> /etc/nginx/sites-available/goali && \
    echo '    }' >> /etc/nginx/sites-available/goali && \
    echo '}' >> /etc/nginx/sites-available/goali

# Enable the site
RUN rm -f /etc/nginx/sites-enabled/default && \
    ln -s /etc/nginx/sites-available/goali /etc/nginx/sites-enabled/

# CRITICAL FIX: Create proper Supervisor configuration with startup dependencies and correct data directory
RUN echo '[supervisord]' > /etc/supervisor/conf.d/goali.conf && \
    echo 'nodaemon=true' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'user=root' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'logfile=/var/log/supervisor/supervisord.log' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'pidfile=/var/run/supervisord.pid' >> /etc/supervisor/conf.d/goali.conf && \
    echo '' >> /etc/supervisor/conf.d/goali.conf && \
    echo '[program:postgresql]' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'command=/usr/lib/postgresql/14/bin/postgres -D /app/postgresql/data' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'user=postgres' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'autostart=true' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'autorestart=true' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'redirect_stderr=true' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'stdout_logfile=/app/logs/postgresql.log' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'priority=100' >> /etc/supervisor/conf.d/goali.conf && \
    echo '' >> /etc/supervisor/conf.d/goali.conf && \
    echo '[program:redis]' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'command=redis-server /etc/redis/redis.conf' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'user=redis' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'autostart=true' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'autorestart=true' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'redirect_stderr=true' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'stdout_logfile=/app/logs/redis.log' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'priority=200' >> /etc/supervisor/conf.d/goali.conf && \
    echo '' >> /etc/supervisor/conf.d/goali.conf && \
    echo '[program:django]' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'command=gunicorn config.asgi:application -k uvicorn.workers.UvicornWorker --bind 127.0.0.1:8001 --workers 2 --timeout 120' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'directory=/app/backend' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'user=goali' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'autostart=true' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'autorestart=true' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'redirect_stderr=true' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'stdout_logfile=/app/logs/django.log' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'environment=DJANGO_SETTINGS_MODULE="config.settings.prod",PYTHONPATH="/app/backend",DATABASE_URL="postgresql://goali:secure_generated_password@localhost:5432/goali_prod",REDIS_URL="redis://127.0.0.1:6379/0",CELERY_BROKER_URL="redis://127.0.0.1:6379/0"' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'priority=400' >> /etc/supervisor/conf.d/goali.conf && \
    echo '' >> /etc/supervisor/conf.d/goali.conf && \
    echo '[program:celery]' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'command=celery -A config worker --loglevel=info --concurrency=1 --max-tasks-per-child=100' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'directory=/app/backend' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'user=goali' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'autostart=true' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'autorestart=true' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'redirect_stderr=true' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'stdout_logfile=/app/logs/celery.log' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'environment=DJANGO_SETTINGS_MODULE="config.settings.prod",PYTHONPATH="/app/backend",DATABASE_URL="postgresql://goali:secure_generated_password@localhost:5432/goali_prod",REDIS_URL="redis://127.0.0.1:6379/0",CELERY_BROKER_URL="redis://127.0.0.1:6379/0"' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'priority=500' >> /etc/supervisor/conf.d/goali.conf && \
    echo '' >> /etc/supervisor/conf.d/goali.conf && \
    echo '[program:nginx]' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'command=nginx -g "daemon off;"' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'autostart=true' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'autorestart=true' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'redirect_stderr=true' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'stdout_logfile=/app/logs/nginx.log' >> /etc/supervisor/conf.d/goali.conf && \
    echo 'priority=600' >> /etc/supervisor/conf.d/goali.conf

# CRITICAL FIX: Create improved startup script that doesn't conflict with supervisor
RUN echo '#!/bin/bash' > /app/start.sh && \
    echo 'set -e' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo 'echo "🚀 Starting Goali All-in-One Container..."' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo '# Set environment variables' >> /app/start.sh && \
    echo 'export DATABASE_URL=${DATABASE_URL:-"postgresql://goali:secure_generated_password@localhost:5432/goali_prod"}' >> /app/start.sh && \
    echo 'export REDIS_URL=${REDIS_URL:-"redis://127.0.0.1:6379/0"}' >> /app/start.sh && \
    echo 'export CELERY_BROKER_URL=${CELERY_BROKER_URL:-"redis://127.0.0.1:6379/0"}' >> /app/start.sh && \
    echo 'export DJANGO_SETTINGS_MODULE="config.settings.prod"' >> /app/start.sh && \
    echo 'export PYTHONPATH="/app/backend"' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo '# Start supervisor which will manage all services' >> /app/start.sh && \
    echo 'echo "🎯 Starting supervisor with all services..."' >> /app/start.sh && \
    echo 'supervisord -c /etc/supervisor/conf.d/goali.conf &' >> /app/start.sh && \
    echo 'SUPERVISOR_PID=$!' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo '# Wait for PostgreSQL to be ready' >> /app/start.sh && \
    echo 'echo "🔄 Waiting for PostgreSQL to be ready..."' >> /app/start.sh && \
    echo 'for i in {1..60}; do' >> /app/start.sh && \
    echo '  if pg_isready -h localhost -p 5432 -U postgres >/dev/null 2>&1; then' >> /app/start.sh && \
    echo '    echo "✅ PostgreSQL is ready"' >> /app/start.sh && \
    echo '    break' >> /app/start.sh && \
    echo '  fi' >> /app/start.sh && \
    echo '  echo "Waiting for PostgreSQL... ($i/60)"' >> /app/start.sh && \
    echo '  sleep 2' >> /app/start.sh && \
    echo 'done' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo '# Wait for Redis to be ready' >> /app/start.sh && \
    echo 'echo "🔄 Waiting for Redis to be ready..."' >> /app/start.sh && \
    echo 'for i in {1..30}; do' >> /app/start.sh && \
    echo '  if redis-cli -h 127.0.0.1 -p 6379 ping >/dev/null 2>&1; then' >> /app/start.sh && \
    echo '    echo "✅ Redis is ready"' >> /app/start.sh && \
    echo '    break' >> /app/start.sh && \
    echo '  fi' >> /app/start.sh && \
    echo '  echo "Waiting for Redis... ($i/30)"' >> /app/start.sh && \
    echo '  sleep 2' >> /app/start.sh && \
    echo 'done' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo '# Setup Django application' >> /app/start.sh && \
    echo 'echo "⚙️ Setting up Django..."' >> /app/start.sh && \
    echo 'cd /app/backend' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo '# Run migrations' >> /app/start.sh && \
    echo 'echo "🔄 Running database migrations..."' >> /app/start.sh && \
    echo 'python3 manage.py migrate --noinput' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo '# Collect static files' >> /app/start.sh && \
    echo 'echo "📁 Collecting static files..."' >> /app/start.sh && \
    echo 'python3 manage.py collectstatic --noinput' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo '# Setup initial data' >> /app/start.sh && \
    echo 'echo "🌱 Setting up initial data..."' >> /app/start.sh && \
    echo 'python3 ultimate_test_setup.py || echo "Setup completed or already done"' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo '# Create superuser if needed' >> /app/start.sh && \
    echo 'echo "👤 Creating superuser..."' >> /app/start.sh && \
    echo 'python3 -c "' >> /app/start.sh && \
    echo 'import django; django.setup()' >> /app/start.sh && \
    echo 'from django.contrib.auth import get_user_model' >> /app/start.sh && \
    echo 'User = get_user_model()' >> /app/start.sh && \
    echo 'if not User.objects.filter(username=\"admin\").exists():' >> /app/start.sh && \
    echo '    User.objects.create_superuser(\"admin\", \"<EMAIL>\", \"0000\")' >> /app/start.sh && \
    echo '    print(\"✅ Superuser created: admin / 0000\")' >> /app/start.sh && \
    echo 'else:' >> /app/start.sh && \
    echo '    print(\"✅ Superuser already exists\")' >> /app/start.sh && \
    echo '"' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo '# Wait for supervisor to exit (keep container running)' >> /app/start.sh && \
    echo 'echo "✅ Application setup complete, supervisor managing services"' >> /app/start.sh && \
    echo 'wait $SUPERVISOR_PID' >> /app/start.sh

# Make startup script executable
RUN chmod +x /app/start.sh

# Set proper ownership for all application files
RUN chown -R goali:goali /app/logs /app/data /app/static /app/media
RUN chown -R postgres:postgres /app/postgresql/ /var/run/postgresql/

# Health check for Digital Ocean
HEALTHCHECK --interval=30s --timeout=10s --start-period=120s --retries=3 \
    CMD curl -f http://localhost:8080/health/ || exit 1

# Expose port (Digital Ocean expects port 8080)
EXPOSE 8080

# Start the application
CMD ["/app/start.sh"]
